name: bitacora
description: Your business's diary.
version: 1.7.8+4241

publish_to: 'none'

environment:
  sdk: '>=3.4.4 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  amplify_auth_cognito: ^2.6.1
  amplify_flutter: ^2.6.1
  amplify_storage_s3: ^2.6.1
  app_settings: ^5.1.1
  archive: ^4.0.5
  async: ^2.13.0
  audioplayers: ^6.1.0
  awesome_notifications: ^0.10.1
  camera: ^0.11.0+2
  collection: ^1.18.0
  connectivity_plus: ^6.0.5
  country_codes: ^3.3.0
  cross_file: ^0.3.4+2
  datetime_picker_formfield_new: ^2.1.0
  device_info_plus: ^10.1.2
  dio: ^5.7.0
  image: ^4.2.0
  intl: ^0.20.2
  ffmpeg_kit_flutter_new: ^1.6.1
  file: ^7.0.0
  file_picker: ^9.0.1
  firebase_analytics: ^11.3.2
  firebase_core: ^3.5.0
  firebase_crashlytics: ^4.1.2
  firebase_messaging: ^15.1.2
  firebase_storage: ^12.4.6
  firebase_vertexai: ^1.6.0
  firebase_remote_config: ^5.4.3
  flutter_image_compress: ^2.3.0
  flutter_map: ^7.0.2
  flutter_map_location_marker: 9.1.1
  flutter_platform_widgets: ^7.0.1
  flutter_svg: ^2.0.10+1
  flutter_typeahead: ^5.2.0
  fluttertoast: ^8.2.12
  flutter_widget_from_html_core: ^0.16.0
  fwfh_url_launcher: ^0.16.0
  geolocator: ^13.0.1
  google_api_availability: ^5.0.0
  google_maps_flutter: ^2.9.0
  hand_signature: ^3.0.3
  heatmap_calendar:
    path: packages/heatmap_calendar
  http_parser: ^4.0.2
  image_picker: ^1.1.2
  ios_force_notification_permission:
    path: packages/ios_force_notification_permission
  latlong2: ^0.9.1
  logger: ^2.4.0
  lzstring: ^2.0.0+2
  map_launcher: ^3.5.0
  mask_text_input_formatter: ^2.9.0
  mime: ^2.0.0
  mixpanel_flutter: ^2.3.4
  native_device_orientation: ^2.0.3
  material_color_utilities: ^0.11.1
  mobile_scanner: ^6.0.6
  omni_datetime_picker: ^2.0.4
  # Due to flutter upgrade, remove when package is updated
  open_filex:
    git:
      url: https://github.com/mufassalhussain/open_filex
  package_info_plus: ^8.0.2
  path: ^1.9.0
  path_provider: ^2.1.5
  percent_indicator: ^4.2.3
  photo_view: ^0.15.0
  provider: ^6.1.2
  record: ^5.1.2
  scrollable_positioned_list: 0.3.8
  share_plus: ^10.1.4
  shared_preferences: ^2.3.2
  shared_preferences_android: ^2.1.1
  shared_preferences_ios: ^2.1.0
  sliding_up_panel: ^2.0.0+1
  speech_to_text: ^7.0.0
  sqflite: ^2.3.3+1
  url_launcher: ^6.3.1
  uuid: ^4.5.0
  widget_to_marker: ^1.0.6
  # Due to flutter upgrade, remove when package is updated
  workmanager:
    git:
      url: https://github.com/fluttercommunity/flutter_workmanager.git
      path: workmanager
      ref: main
  modal_bottom_sheet: ^3.0.0
  camera_android: ^0.10.10
  injectzone: ^0.0.2
  fl_chart: ^0.70.2
  sentry_flutter: ^8.14.1
  easy_video_editor: ^0.0.5

dependency_overrides:
  web: ^0.5.1
  # Due to flutter upgrade, remove when package is updated
  google_api_availability_android:
    git:
      url: https://github.com/postflow/flutter-google-api-availability.git
      ref: main
      path: google_api_availability_android
  # Due to flutter upgrade, remove when package is updated
  url_launcher_android: 6.3.14
  intl: ^0.19.0
  async: ^2.12.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  flutter_launcher_icons: ^0.14.1
  flutter_lints: 5.0.0
  flutter_native_splash: ^2.4.1
  path_provider_platform_interface: ^2.0.4
  mocktail: ^1.0.4
  test: ^1.24.9
  sentry_dart_plugin: ^2.0.0

# The following section is specific to Flutter.
flutter:
  generate: true
  uses-material-design: true
  assets:
    - images/
    - images/2.0x/
    - images/3.0x/
    - map_styles/
    - images/onboarding/
    - images/avatars/

  fonts:
    - family: BitacoraIcons
      fonts:
        - asset: fonts/BitacoraIcons.ttf
    - family: CerebriSans
      fonts:
        - asset: fonts/CerebriSansPro-Bold.otf
          weight: 700

