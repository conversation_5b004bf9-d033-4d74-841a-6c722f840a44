import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/application/bug_report/bug_report_service.dart';
import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/dev/command/dev_command.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class DevCommandBugReport extends DevCommand {
  DevCommandBugReport() : super('bug-report', Icons.bug_report, _run);

  static Future<void> _run(BuildContext context) async {
    await BugReportService().createAndSend(
      context.read<Repository>(),
      context.read<ActiveSession>().value,
      context.read<AnalyticsLogger>(),
      isSimpleName: true,
      description: 'Created by DevTools.',
      shouldUploadToAmplify: false,
      shouldLogAnalytic: false,
    );
  }
}
