import 'dart:async';

import 'package:bitacora/analytics/analytics_events.dart';
import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/application/api/api_helper.dart';
import 'package:bitacora/application/nuke.dart';
import 'package:bitacora/domain/auth/auth_repository.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/translator.dart';
import 'package:bitacora/domain/organization/value/organization_user_has_seen.dart';
import 'package:bitacora/domain/session/session.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/infrastructure/organization/organization_api_translator.dart';
import 'package:bitacora/l10n/app_localizations_resolver.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:bitacora/util/restart_widget.dart';
import 'package:dio/dio.dart';
import 'package:flutter/widgets.dart';

const _kLoginPath = 'sessions/login';
const _kSignupPath = 'users/create';

const kAuthRestartDataKey = 'auth_restart';

class AuthError extends Error {
  String error;

  AuthError(this.error);

  @override
  String toString() {
    return error;
  }
}

class AuthAppService {
  final ApiHelper _apiHelper;
  final Repository _db;
  final Translator _apiTranslator;
  final AuthRepository _authRepository;
  final AnalyticsLogger _analyticsLogger;

  AuthAppService({
    required ApiHelper apiHelper,
    required Repository db,
    required Translator apiTranslator,
    required AuthRepository authRepository,
    required AnalyticsLogger analyticsLogger,
  })  : _apiHelper = apiHelper,
        _authRepository = authRepository,
        _db = db,
        _apiTranslator = apiTranslator,
        _analyticsLogger = analyticsLogger;

  Future<void> login({
    required BuildContext context,
    required String locale,
    required String email,
    required String password,
  }) {
    return _startSession(
      context,
      _kLoginPath,
      AnalyticsEvent.login,
      {'email': email, 'password': password, 'locale': locale},
    );
  }

  Future<void> signup({
    required BuildContext context,
    required String locale,
    required String name,
    required String email,
    required String password,
    String? phoneNumber,
  }) {
    return _startSession(
      context,
      _kSignupPath,
      AnalyticsEvent.signup,
      {
        'name': name,
        'email': email,
        'password': password,
        'locale': locale,
        if (phoneNumber != null) 'phone': phoneNumber,
      },
    );
  }

  Future<String> resetPassword({
    required String locale,
    required String email,
  }) async {
    try {
      final response = await _apiHelper.get(
        'users/reset_password',
        queryParameters: {'email': email, 'locale': locale},
      );

      return response.data['message'];
    } on DioException catch (e) {
      throw AuthError(e.response?.data['error']);
    } catch (_) {
      rethrow;
    }
  }

  Future<void> logout(NukeContextSnapshot contextSnapshot) async {
    try {
      await _apiHelper.get('sessions/logout');
    } catch (_) {
      logger.f('Failed to sessions/logout');
    }
    return Nuke().nuke(contextSnapshot);
  }

  Future<void> _startSession(
    BuildContext context,
    String path,
    AnalyticsEvent analyticsEvent,
    Map<String, String> payload,
  ) async {
    final contextSnapshot = NukeContextSnapshot(context);

    try {
      final response = await _apiHelper.post<Map>(path, data: payload);
      final token = _getTokenFromHeader(response);

      String? remoteUserId;
      await _db.transaction((context) async {
        final userData = response.data!['user'];
        final user = _apiTranslator.user.fromMap(userData);
        remoteUserId = '${user.remoteId!.value}';
        final userId = (await _db.user.safeSave(context, user))!;

        List orgsData = response.data!['organizations'];
        for (final orgData in orgsData) {
          final organization = await (const OrganizationApiTranslator())
              .fromMapWithColorIfNew(context, orgData);
          await _db.organization.save(
            context,
            organization.copyWith(
              userHasSeen: const OrganizationUserHasSeen(true),
            ),
          );
        }

        await _authRepository
            .save(Session(token: SessionToken(token), user: User(id: userId)));
      });

      await _analyticsLogger.setUserId(remoteUserId);
      await _analyticsLogger.logEvent(analyticsEvent,
          props: {kAnalyticsPropUserId: remoteUserId!});
    } on DioException catch (e) {
      throw AuthError(e.response?.data['error']);
    } catch (_) {
      await Nuke()
          .nuke(contextSnapshot, RestartData({kAuthRestartDataKey: payload}));
      rethrow;
    }
  }

  Future<void> recoverSession({
    required Session invalidSession,
    required String locale,
    required String email,
    required String password,
  }) async {
    try {
      final response = await _apiHelper.post<Map>(
        _kLoginPath,
        data: {'email': email, 'password': password, 'locale': locale},
      );
      final token = _getTokenFromHeader(response);

      final userData = response.data!['user'];
      final user = _apiTranslator.user.fromMap(userData);
      if (user.remoteId != invalidSession.user.remoteId) {
        throw AuthError(
          AppLocalizationsResolver.get().credentialNotMatchWithCurrentUser,
        );
      }
      await _authRepository
          .save(Session(token: SessionToken(token), user: invalidSession.user));
    } on DioException catch (e) {
      throw AuthError(e.response?.data['error']);
    }
  }

  String _getTokenFromHeader(Response response) {
    final authorization = response.headers.value('authorization');
    const prefix = 'Token token=';

    if (authorization != null && authorization.startsWith(prefix)) {
      return authorization.replaceAll(prefix, '');
    }

    throw Exception('No authorization header in response');
  }
}
