import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/outgoing_mutation/outgoing_mutation.dart';

class PendingMutationsUploadRepositoryQuery
    extends RepositoryQuery<List<OutgoingMutation>> {
  const PendingMutationsUploadRepositoryQuery();

  @override
  Future<List<OutgoingMutation>> run(RepositoryQueryContext context) {
    return context.db.outgoingMutation.findPending(context);
  }

  @override
  Fields? fields(Repository<RepositoryQueryContext> db) =>
      db.outgoingMutation.fieldsBuilder.build();
}
