import 'package:bitacora/domain/common/repository_cache.dart';
import 'package:bitacora/domain/common/repository_table_cache.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:flutter/foundation.dart';

class ProjectRepositoryCache extends RepositoryTableCache<Project> {
  @override
  @protected
  Project? completeCachedModel(RepositoryCache cache, Project model) {
    final orgId = model.organization?.id;
    if (orgId != null) {
      final organization =
          cache.table<Organization>()!.cachedModel(cache, orgId);
      return merge(model, Project(organization: organization));
    }
    return model;
  }

  @override
  @protected
  bool isContainedIn(RepositoryCache cache, Project a, Project b) {
    return isNullOrEqual(a.id, b.id) &&
        isNullOrEqual(a.remoteId, b.remoteId) &&
        isNullOrEqual(a.name, b.name) &&
        isNullOrEqual(a.description, b.description) &&
        isNullOrEqual(a.address, b.address) &&
        isNullOrEqual(a.location, b.location) &&
        isNullOrEqual(a.type, b.type) &&
        isNullOrEqual(a.isSyncable, b.isSyncable) &&
        isNullOrEqual(a.syncLastEntryUpdatedAt, b.syncLastEntryUpdatedAt) &&
        isNullOrEqual(a.syncLastSyncTime, b.syncLastSyncTime) &&
        isNullOrEqual(a.syncNextPageToken, b.syncNextPageToken) &&
        isNestedContainedIn(cache, a.organization, b.organization);
  }

  @override
  @protected
  Project merge(Project a, Project b) {
    if (a.id != null && b.id != null && a.id != b.id) {
      throw 'Merging models with different id';
    }

    return Project(
      id: b.id ?? a.id,
      remoteId: b.remoteId ?? a.remoteId,
      name: b.name ?? a.name,
      description: b.description ?? a.description,
      address: b.address ?? a.address,
      location: b.location ?? a.location,
      type: b.type ?? a.type,
      isSyncable: b.isSyncable ?? a.isSyncable,
      syncLastEntryUpdatedAt:
          b.syncLastEntryUpdatedAt ?? a.syncLastEntryUpdatedAt,
      syncLastSyncTime: b.syncLastSyncTime ?? a.syncLastSyncTime,
      syncNextPageToken: b.syncNextPageToken ?? a.syncNextPageToken,
      organization: b.organization ?? a.organization,
    );
  }

  @override
  @protected
  Project prepareToCache(RepositoryCache cache, LocalId id, Project model) {
    final orgId = cache.table<Organization>()?.resolveId(model.organization);
    return merge(
      model,
      Project(
        id: id,
        organization: orgId == null ? null : Organization(id: orgId),
      ),
    );
  }
}
