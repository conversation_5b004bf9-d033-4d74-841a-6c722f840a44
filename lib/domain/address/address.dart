import 'package:bitacora/domain/address/value/address_created_at.dart';
import 'package:bitacora/domain/address/value/address_updated_at.dart';
import 'package:bitacora/domain/address/value/address_value.dart';
import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/common/person_detail_contact_type.dart';
import 'package:bitacora/domain/person_detail/person_detail.dart';

export 'package:bitacora/domain/address/value/address_created_at.dart';
export 'package:bitacora/domain/address/value/address_updated_at.dart';
export 'package:bitacora/domain/address/value/address_value.dart';
export 'package:bitacora/domain/common/person_detail_contact_type.dart';

class Address extends Model {
  final AddressValue? value;
  final PersonDetailContactType? type;
  final AddressUpdatedAt? updatedAt;
  final AddressCreatedAt? createdAt;

  final PersonDetail? personDetail;

  Address({
    super.id,
    super.remoteId,
    this.value,
    this.type,
    this.updatedAt,
    this.createdAt,
    this.personDetail,
  });

  @override
  Map<Field, dynamic> get fields => {
        AddressField.id: id,
        AddressField.remoteId: remoteId,
        AddressField.value: value,
        AddressField.type: type,
        AddressField.updatedAt: updatedAt,
        AddressField.createdAt: createdAt,
        AddressField.personDetail: personDetail,
      };
}

enum AddressField with Field {
  id,
  remoteId,
  value,
  type,
  updatedAt,
  createdAt,
  personDetail
}

const addressNestedModelFields = {AddressField.personDetail};
