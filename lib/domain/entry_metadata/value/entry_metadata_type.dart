import 'package:bitacora/domain/common/value_object/value_object.dart';

enum EntryMetadataTypeValue {
  sentiment('sentiment'),
  keyword('keyword'),
  actionItem('actionItem'),
  healthItem('healthItem'),
  healthTag('healthTag');

  final String apiValue;

  const EntryMetadataTypeValue(this.apiValue);

  static EntryMetadataTypeValue fromString(String value) {
    switch (value) {
      case 'sentiment':
        return EntryMetadataTypeValue.sentiment;
      case 'keywords':
        return EntryMetadataTypeValue.keyword;
      case 'actionItems':
        return EntryMetadataTypeValue.actionItem;
      case 'healthItems':
        return EntryMetadataTypeValue.healthItem;
      case 'healthTags':
        return EntryMetadataTypeValue.healthTag;
      default:
        throw 'EntryMetadataTypeValue: [$value] not supported';
    }
  }
}

class EntryMetadataType extends ValueObject<EntryMetadataTypeValue> {
  static const EntryMetadataType sentiment =
      EntryMetadataType(EntryMetadataTypeValue.sentiment);
  static const EntryMetadataType keyword =
      EntryMetadataType(EntryMetadataTypeValue.keyword);
  static const EntryMetadataType actionItem =
      EntryMetadataType(EntryMetadataTypeValue.actionItem);
  static const EntryMetadataType healthItem =
      EntryMetadataType(EntryMetadataTypeValue.healthItem);
  static const EntryMetadataType healthTag =
      EntryMetadataType(EntryMetadataTypeValue.healthTag);
  static const List<EntryMetadataType> values = [
    sentiment,
    keyword,
    actionItem,
    healthItem,
    healthTag
  ];

  const EntryMetadataType(super.value);

  factory EntryMetadataType.fromDbValue(int dbValue) => values[dbValue];

  factory EntryMetadataType.fromApiValue(String apiValue) =>
      EntryMetadataType(EntryMetadataTypeValue.fromString(apiValue));

  @override
  bool isSameType(Object other) {
    return other is EntryMetadataType;
  }

  @override
  int get dbValue => value.index;

  @override
  String get apiValue => value.toString().split('.').last;
}
