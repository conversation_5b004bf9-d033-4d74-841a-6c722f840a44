import 'package:bitacora/domain/custom_field_allowed_value/custom_field_allowed_value.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/controller/template_block_allowed_values_form_controller.dart';
import 'package:bitacora/presentation/entry_form/util/typeahead/typeahead_overlay.dart';
import 'package:bitacora/presentation/entry_form/util/typeahead/typeahead_text_field_configuration.dart';
import 'package:bitacora/util/trim_text_on_focus_lost.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class SuggestionTypeaheadAllowedValues extends StatefulWidget {
  final TemplateBlockAllowedValuesFormController controller;
  final ValueSetter<CustomFieldAllowedValue?> onSelect;
  final TypeaheadTextFieldConfiguration textFieldConfiguration;
  final bool hideOnSelect;
  final bool highlightMatches;

  const SuggestionTypeaheadAllowedValues({
    super.key,
    required this.controller,
    required this.onSelect,
    required this.textFieldConfiguration,
    this.hideOnSelect = true,
    this.highlightMatches = true,
  });

  @override
  State<SuggestionTypeaheadAllowedValues> createState() =>
      _SuggestionTypeaheadAllowedValuesState();
}

class _SuggestionTypeaheadAllowedValuesState
    extends State<SuggestionTypeaheadAllowedValues> {
  final LayerLink _layerLink = LayerLink();
  TypeaheadOverlayController? _overlay;

  @override
  void initState() {
    super.initState();

    widget.controller.focusNode.addListener(_onFocusChange);
    widget.controller.suggestedAllowedValues.addListener(_onItemsChange);
  }

  @override
  void dispose() {
    widget.controller.focusNode.removeListener(_onFocusChange);
    widget.controller.suggestedAllowedValues.removeListener(_onItemsChange);
    _overlay?.hide();
    super.dispose();
  }

  void _onFocusChange() {
    _overlay ??= _setupOverlay();
    if (!widget.controller.focusNode.hasFocus) {
      _overlay!.hide();
      return;
    }

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final overlayState = Overlay.of(context);
      final renderBox = context.findRenderObject() as RenderBox;

      if (mounted) {
        _overlay!.maybeShow(
          overlayState,
          renderBox,
          widget.controller.suggestedAllowedValues.value,
        );
      }
    });
  }

  void _onItemsChange() {
    _overlay ??= _setupOverlay();
    final overlayState = Overlay.of(context);
    final renderBox = context.findRenderObject() as RenderBox;
    final items = widget.controller.suggestedAllowedValues.value;

    if (items.length == 1 &&
        items.first.value! == widget.controller.value.value?.value) {
      _overlay!.hide();
      return;
    }

    if (items.isNotEmpty) {
      _overlay!.maybeShow(overlayState, renderBox, items);
    } else {
      _overlay!.hide();
    }
  }

  TypeaheadOverlayController _setupOverlay() {
    final brightness = MediaQuery.platformBrightnessOf(context);
    return TypeaheadOverlayController<CustomFieldAllowedValue?>(
      scrollController: context.read<ScrollController>(),
      isVerticalDragGesture: context.read<ValueNotifier<bool>>(),
      layerLink: _layerLink,
      focusNode: widget.textFieldConfiguration.focusNode,
      textController: widget.textFieldConfiguration.controller,
      onSelect: widget.onSelect,
      borderColor: Theme.of(context).colorScheme.primary,
      itemStringResolver: (value) => value?.value?.displayValue ?? '',
      backgroundColor: brightness == Brightness.light
          ? Colors.white
          : Theme.of(context).colorScheme.surfaceContainer,
    );
  }

  @override
  Widget build(BuildContext context) {
    return TrimTextOnFocusLost(
      controller: widget.controller.textController,
      focusNode: widget.controller.focusNode,
      child: widget.textFieldConfiguration
          .copyWith(
            scrollPadding: const EdgeInsets.all(20)
                .copyWith(bottom: MediaQuery.sizeOf(context).height * 0.3),
            decoration: widget.textFieldConfiguration.decoration!.copyWith(
              suffixIcon: ValueListenableBuilder(
                valueListenable: widget.textFieldConfiguration.controller,
                builder: (context, _, __) {
                  return widget.controller.value.value == null
                      ? const SizedBox()
                      : IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () {
                            if (widget.controller.value.value != null) {
                              widget.controller.value.value = null;
                              return;
                            }
                            if (widget.textFieldConfiguration.controller.text
                                .isNotEmpty) {
                              widget.textFieldConfiguration.controller.text =
                                  '';
                            }
                          },
                        );
                },
              ),
            ),
          )
          .builder(
            context,
            widget.controller.textController,
            widget.controller.focusNode,
            _layerLink,
          ),
    );
  }
}
