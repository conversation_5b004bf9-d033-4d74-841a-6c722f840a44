import 'package:bitacora/domain/attachment/doodle_drawn_line.dart';
import 'package:bitacora/presentation/entry_form/options/attachment/image/doodle/doodle_sketcher.dart';
import 'package:flutter/material.dart';

class DoodleCanvasAllLines extends StatelessWidget {
  final GlobalKey repaintGlobalKey;
  final Size canvasSize;
  final List<DoodleDrawnLine> imageLines;
  final double imageScale;
  final Size imageSize;
  final Offset imageOffset;

  const DoodleCanvasAllLines({
    super.key,
    required this.repaintGlobalKey,
    required this.canvasSize,
    required this.imageLines,
    required this.imageScale,
    required this.imageSize,
    required this.imageOffset,
  });

  @override
  Widget build(BuildContext context) {
    final linesToDraw = imageLines
        .map((e) => e.copyWith(
            points: e.points.map((p) => _imagePointToUi(p!)).toList()))
        .toList();
    return IgnorePointer(
      child: RepaintBoundary(
        key: repaint<PERSON><PERSON><PERSON><PERSON><PERSON>,
        child: CustomPaint(
          painter: DoodleSketcher(
            lines: linesToDraw,
            canvasScale: imageScale,
            scaleLines: true,
          ),
        ),
      ),
    );
  }

  Offset _imagePointToUi(Offset imgPoint) {
    final viewport = Offset(canvasSize.width, canvasSize.height);
    final imageSizeOffset = Offset(imageSize.width, imageSize.height);
    final offset =
        ((viewport - (imageSizeOffset * imageScale)) / 2) + imageOffset;

    return (imgPoint * imageScale) + offset;
  }
}
