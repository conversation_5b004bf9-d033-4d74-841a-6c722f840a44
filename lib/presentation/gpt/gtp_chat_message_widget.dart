import 'package:bitacora/domain/gpt/gpt_chat_message.dart';
import 'package:bitacora/presentation/theme/theme_util.dart';
import 'package:bitacora/util/share.dart';
import 'package:flutter/material.dart';

class GptChatMessageWidget extends StatelessWidget {
  final GptChatMessage message;
  final bool hasError;

  const GptChatMessageWidget(
      {super.key, required this.message, this.hasError = false});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Expanded(
            child: Align(
              alignment: (message.type.isAssistant
                  ? Alignment.topLeft
                  : Alignment.topRight),
              child: FractionallySizedBox(
                widthFactor: message.type.isAssistant ? 1 : 0.9,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16.0),
                    color: hasError
                        ? Theme.of(context).colorScheme.error.withValues(alpha: 0.15)
                        : (message.type.isAssistant
                            ? Colors.grey.withValues(alpha: 0.15)
                            : Theme.of(context)
                                .colorScheme
                                .primary
                                .withValues(alpha: 0.15)),
                  ),
                  padding: kPageInsets,
                  child: Text(
                    message.content.value,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
              ),
            ),
          ),
          if (message.type.isAssistant && !hasError) ...[
            const SizedBox(width: 8.0),
            InkWell(
              onTap: _onShare,
              child: CircleAvatar(
                backgroundColor: Colors.grey.withValues(alpha: 0.15),
                radius: 16,
                child: Icon(
                  Icons.share,
                  size: 16,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ),
          ]
        ],
      ),
    );
  }

  void _onShare() {
    Share().share(message.content.value);
  }
}
