import 'dart:async';

import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/qr/qr_code_decoder.dart';
import 'package:bitacora/application/qr/translator/qr_code_translator.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/qr_code/qr_code.dart';
import 'package:bitacora/domain/qr_code/value/qr_code_action.dart';
import 'package:bitacora/domain/qr_identifier/qr_indentifier.dart';
import 'package:bitacora/domain/resource/resource.dart';
import 'package:bitacora/domain/resource/value/resource_aggregation_entity_type.dart';
import 'package:bitacora/presentation/entry_form/entry_form_controller.dart';
import 'package:bitacora/presentation/entry_form/entry_form_controller_context_snapshot.dart';
import 'package:bitacora/presentation/entry_form/entry_form_page.dart';
import 'package:bitacora/presentation/entry_form/entry_form_page_navigator_props.dart';
import 'package:bitacora/presentation/qr/qr_code_last_updated_repository_query.dart';
import 'package:bitacora/presentation/qr/qr_resources_repository_query.dart';
import 'package:bitacora/util/location_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:latlong2/latlong.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:provider/provider.dart';

const _kQrBottomSheetAnimationDuration = Duration(milliseconds: 256);
const _maxScanDistanceMeters = 30;

enum QrCodeDecodingState {
  idle,
  started,
  finished,
  failed,
}

class QrBottomSheetController {
  final QrCodeTranslator qrCodeTranslator = QrCodeTranslator();
  final ValueNotifier<QrCodeDecodingState> state =
      ValueNotifier(QrCodeDecodingState.idle);
  final void Function(QrBottomSheetController controller) showBottomSheet;
  final void Function() afterClose;

  QrCode? qrCode;
  List<Resource> resources = [];
  EntryFormController? entryFormController;
  QrIdentifier? identifier;
  bool locationIsAllowed = true;
  bool belongsToActiveOrganization = true;
  bool isEditable = false;

  QrBottomSheetController({
    required this.showBottomSheet,
    required this.afterClose,
  });

  void processBarCode(
    BuildContext context,
    Barcode code,
  ) async {
    if (state.value != QrCodeDecodingState.idle) {
      return;
    }

    if (code.rawValue == null) {
      state.value = QrCodeDecodingState.failed;
      return;
    }

    state.value = QrCodeDecodingState.started;

    final db = context.read<Repository>();
    final activeSession = context.read<ActiveSession>();
    final activeOrganization = context.read<ActiveOrganization>();

    final entryFormPageContextSnapshot = EntryFormPageContextSnapshot(context);
    final entryFormControllerContextSnapshot =
        EntryFormControllerContextSnapshot(context);

    try {
      qrCode = await QrCodeDecoder().decode(code);
      qrCode =
          await db.query(QrCodeLastUpdatedRepositoryQuery(qrCode: qrCode!)) ??
              qrCode;

      _checkIfBelongsToActiveOrg(activeOrganization);

      await _maybeCheckIfLocationIsAllowed();

      isEditable = qrCode!.payload!.isEditable;
      await _decode(
        db,
        activeSession,
        entryFormPageContextSnapshot,
        entryFormControllerContextSnapshot,
      );
    } catch (_) {
      _expandPanel(true);
      rethrow;
    }
  }

  void _checkIfBelongsToActiveOrg(ActiveOrganization activeOrganization) {
    if (qrCode!.organization!.remoteId != activeOrganization.value!.remoteId) {
      belongsToActiveOrganization = false;

      throw 'This QR code does not belong to the active organization';
    }
  }

  Future<void> _maybeCheckIfLocationIsAllowed() async {
    if (!qrCode!.payload!.checkLocation) {
      return;
    }

    final LocationUtils locationUtils = LocationUtils();
    final position = await locationUtils.determinePosition();

    final qrLocation =
        LatLng(qrCode!.payload!.latitude!, qrCode!.payload!.longitude!);

    final distance = LocationUtils().calculateDistanceInMeters(
      LatLng(position.latitude, position.longitude),
      qrLocation,
    );

    if (distance > _maxScanDistanceMeters) {
      locationIsAllowed = false;

      throw 'Too far from the location of the QR code';
    }
  }

  Future<void> _decode(
    Repository db,
    ActiveSession activeSession,
    EntryFormPageContextSnapshot entryFormPageContextSnapshot,
    EntryFormControllerContextSnapshot entryFormControllerContextSnapshot,
  ) async {
    resources = await db.query(QrResourcesRepositoryQuery(
      qrRemoteId: qrCode!.remoteId!,
      entityType: ResourceAggregationEntityType.qrCode,
    ));

    switch (qrCode!.payload!.action) {
      case QrCodeAction.saveEntry:
        if (qrCode!.payload!.isEditable && resources.isEmpty) {
          final inputEntry = qrCodeTranslator.toEntry(qrCode!);
          await EntryFormPage.navigate(
            EntryFormPageNavigatorProps.fromContextSnapshot(
              entryFormPageContextSnapshot,
              inputEntry,
            ),
            qrCode: qrCode,
          );
          onClosePanel();
          return;
        }

        entryFormController = qrCodeTranslator.toEntryFormController(
          activeSession,
          entryFormControllerContextSnapshot,
          qrCode!,
        );
        _expandPanel();
        break;
      case QrCodeAction.checkInOut:
        entryFormController =
            await qrCodeTranslator.toPersonnellogFormControllerFromCheckInOut(
          db,
          activeSession,
          entryFormControllerContextSnapshot,
          qrCode!,
        );
        _expandPanel();
        break;
      case QrCodeAction.identifier:
        identifier = qrCodeTranslator.toIdentifier(qrCode!);
        _expandPanel();
        break;
    }
  }

  void _expandPanel([bool hasError = false]) {
    state.value =
        hasError ? QrCodeDecodingState.failed : QrCodeDecodingState.finished;

    showBottomSheet(this);
  }

  void onClosePanel() {
    Future.delayed(_kQrBottomSheetAnimationDuration, () {
      locationIsAllowed = true;
      entryFormController = null;
      belongsToActiveOrganization = true;
      state.value = QrCodeDecodingState.idle;
      resources = [];
      afterClose();
    });
  }

  void editEntry(BuildContext context) async {
    final db = context.read<Repository>();
    final activeSession = context.read<ActiveSession>();
    final contextSnapshot = EntryFormPageContextSnapshot(context);

    final inputEntry = qrCode!.payload!.action == QrCodeAction.checkInOut
        ? await QrCodeTranslator()
            .toEntryFromCheckInOut(db, activeSession, qrCode!)
        : QrCodeTranslator().toEntry(qrCode!);
    await EntryFormPage.navigate(
      EntryFormPageNavigatorProps.fromContextSnapshot(
          contextSnapshot, inputEntry),
      qrCode: qrCode,
    );
  }
}
