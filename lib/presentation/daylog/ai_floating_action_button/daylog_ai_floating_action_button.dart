import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/product/value/product_uuid.dart';
import 'package:bitacora/presentation/daylog/ai_floating_action_button/daylog_ai_option_button.dart';
import 'package:bitacora/presentation/daylog/ai_floating_action_button/daylog_ai_option_controller.dart';
import 'package:bitacora/presentation/daylog/ai_floating_action_button/generator/daylog_ai_button_generator.dart';
import 'package:bitacora/presentation/daylog/ai_floating_action_button/has_ai_product_repository_query.dart';
import 'package:bitacora/presentation/daylog/audio/simple_audio_recorder.dart';
import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:bitacora/presentation/daylog/ai_floating_action_button/daylog_ai_processing_button.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

const _kAiButtonWidth = 200.0;
const _kAiButtonHeight = 48.0;
const _kAiOptionDistance = 56.0;
const _kAiProductUuid = '6c6b5a4d-5e4f-6c3b-7d8e-7a8b9c0d1e2f';

const _kAiScaleStart = 0.8;
const _kAiScaleEnd = 1.0;

enum ProcessingState { idle, processing, completed }

class AiFloatingActionButton extends StatefulWidget {
  const AiFloatingActionButton({
    super.key,
  });

  @override
  State<AiFloatingActionButton> createState() => _AiFloatingActionButtonState();
}

class _AiFloatingActionButtonState extends State<AiFloatingActionButton>
    with TickerProviderStateMixin {
  final DaylogAiOptionController controller = DaylogAiOptionController();

  @override
  void initState() {
    super.initState();
    controller.setup(this);
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final db = context.read<Repository>();
    final session = context.read<ActiveSession>().value!;
    final activeOrganization = context.watch<ActiveOrganization>();

    return StreamBuilder(
        stream: db.product.getMutations(),
        builder: (context, _) {
          print('----> product:mutation!');
          return FutureBuilder(
              future: db.query(
                HasAiProductRepositoryQuery(
                  session.user.id!,
                  activeOrganization.value!.id!,
                  ProductUuid(_kAiProductUuid),
                ),
              ),
              builder: (context, hasProductSnapshot) {
                if (!hasProductSnapshot.hasData || !hasProductSnapshot.data!) {
                  return const SizedBox();
                }

                return Padding(
                  padding: EdgeInsets.only(bottom: 6.0),
                  child: SizedBox(
                    width: _kAiButtonWidth,
                    height: _kAiButtonHeight,
                    child: Stack(
                      alignment: Alignment.bottomRight,
                      clipBehavior: Clip.none,
                      children: [
                        AnimatedBuilder(
                          animation: controller.animationController.animation,
                          builder: (context, child) {
                            return Positioned(
                              right: _kAiOptionDistance * 2 +
                                  4.0 +
                                  _kAiOptionDistance *
                                      controller
                                          .animationController.animation.value,
                              bottom: 0,
                              child: Opacity(
                                opacity: controller
                                    .animationController.animation.value,
                                child: Transform.scale(
                                  scale: _kAiScaleStart +
                                      (_kAiScaleEnd - _kAiScaleStart) *
                                          controller.animationController
                                              .animation.value,
                                  child: DaylogAiOptionButton(
                                    icon: Icons.video_camera_back,
                                    color: bitacoraBlue,
                                    onPressed: () {
                                      controller.handleGeneration(
                                          context, DaylogAiButtonSource.video);
                                    },
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                        AnimatedBuilder(
                          animation: controller.animationController.animation,
                          builder: (context, child) {
                            return Positioned(
                              right: _kAiOptionDistance +
                                  4.0 +
                                  _kAiOptionDistance *
                                      controller
                                          .animationController.animation.value,
                              bottom: 0,
                              child: Opacity(
                                opacity: controller
                                    .animationController.animation.value,
                                child: Transform.scale(
                                  scale: _kAiScaleStart +
                                      (_kAiScaleEnd - _kAiScaleStart) *
                                          controller.animationController
                                              .animation.value,
                                  child: DaylogAiOptionButton(
                                    icon: Icons.photo_library_outlined,
                                    color: bitacoraYellow,
                                    onPressed: () {
                                      controller.handleGeneration(
                                          context, DaylogAiButtonSource.image);
                                    },
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                        AnimatedBuilder(
                          animation: controller.animationController.animation,
                          builder: (context, child) {
                            return Positioned(
                              right: _kAiOptionDistance + 4.0,
                              bottom: 0,
                              child: Opacity(
                                opacity: controller
                                    .animationController.animation.value,
                                child: Transform.scale(
                                  scale: _kAiScaleStart +
                                      (_kAiScaleEnd - _kAiScaleStart) *
                                          controller.animationController
                                              .animation.value,
                                  child: DaylogAiOptionButton(
                                    icon: Icons.mic,
                                    color: bitacoraGreen,
                                    onPressed: () {
                                      controller.handleGeneration(
                                          context, DaylogAiButtonSource.audio);
                                    },
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                        Positioned(
                          right: 4.0,
                          child: ValueListenableBuilder<ProcessingState>(
                            valueListenable: controller.processingState,
                            builder: (context, state, _) {
                              return state != ProcessingState.idle
                                  ? DaylogAiProcessingButton(state: state)
                                  : ValueListenableBuilder(
                                      valueListenable: controller.isExpanded,
                                      builder: (context, isExpanded, _) {
                                        return FloatingActionButton(
                                          heroTag: 'fab-ai',
                                          onPressed: controller.toggleAnimation,
                                          mini: true,
                                          backgroundColor: isExpanded
                                              ? bitacoraPurple[500]
                                              : bitacoraPurple[900],
                                          child: Icon(
                                            Icons.auto_awesome,
                                            color: Colors.white,
                                          ),
                                        );
                                      },
                                    );
                            },
                          ),
                        ),
                        ValueListenableBuilder(
                          valueListenable:
                              controller.audioRecorderControllerNotifier,
                          builder: (context, audioRecorderController, _) {
                            if (audioRecorderController != null) {
                              return Positioned(
                                right: _kAiOptionDistance + 4.0,
                                bottom: 0,
                                child: SizedBox(
                                  height: 48.0,
                                  child: SimpleAudioRecorder(
                                    controller: controller
                                        .audioRecorderControllerNotifier.value!,
                                    onCancel:
                                        controller.handleAudioRecordingCancel,
                                    onSave: controller.handleAudioRecordingSave,
                                  ),
                                ),
                              );
                            }
                            return const SizedBox();
                          },
                        ),
                      ],
                    ),
                  ),
                );
              });
        });
  }
}
