import 'dart:async';

import 'package:bitacora/analytics/analytics_events.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/presentation/daylog/ai_floating_action_button/generator/daylog_ai_button_generator.dart';
import 'package:bitacora/util/file_system.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:bitacora/util/storage/storage_utils.dart';
import 'package:firebase_vertexai/firebase_vertexai.dart';
import 'package:flutter/foundation.dart';
import 'package:mime/mime.dart';
import 'package:path/path.dart' as path;
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';

class DaylogAiButtonFromVideoGenerator extends DaylogAiButtonSourceGenerator {
  DaylogAiButtonFromVideoGenerator(super.contextSnapshot);

  @override
  DaylogAiButtonSource get source => DaylogAiButtonSource.video;

  @override
  AnalyticsEvent get event => AnalyticsEvent.aiVideoEntrySelected;

  @override
  Future<Entry> runInternal() async {
    logAnalytics();

    final file = (await pickFiles()).first;
    durationTracker.markStart();
    logger.i('ai-generation:from video process started');

    logger.i('ai-generation:starting video compression [$durationTracker]');
    final compressed = await _compressVideo(file);
    logger.i('ai-generation:video compression completed [$durationTracker]');

    logger.i('ai-generation:determining mime type [$durationTracker]');
    final mimeType = lookupMimeType(compressed.path)!;
    logger.i('ai-generation:detected mime type: $mimeType [$durationTracker]');

    logger.i('ai-generation:uploading compressed video [$durationTracker]');
    final fileUri = (await uploadTempFiles([compressed])).first;
    logger.i('ai-generation:video upload completed [$durationTracker]');

    logger.i('ai-generation:creating file data part [$durationTracker]');
    final part = FileData(mimeType, fileUri);

    logger.i(
        'ai-generation:starting parallel operations (AI generation + location) [$durationTracker]');
    final (generatedEntry, location) =
        await (generate([part]), getEntryLocation()).wait;
    logger.i(
        'ai-generation:AI generation and location retrieval completed [$durationTracker]');

    logger.i('ai-generation:cleaning up temporary files [$durationTracker]');
    unawaited(deleteTempFiles([compressed]));

    logger.i(
        'ai-generation:processing original file to attachment [$durationTracker]');
    final attachment = await processFileToAttachment(file);
    logger
        .i('ai-generation:attachment processing completed [$durationTracker]');

    logger.i('ai-generation:creating final entry object [$durationTracker]');
    final finalEntry = generatedEntry.copyWith(
      attachments: [],
      tags: [],
      location: location,
      source: generatedEntry.source!.copyWith(attachments: [attachment]),
    );

    logger.i(
        'ai-generation:from video process completed successfully [$durationTracker]');

    return finalEntry;
  }

  Future<File> _compressVideo(File input) async {
    logger.i(
        'ai-generation:initializing video compression for: ${path.basename(input.path)} [$durationTracker]');

    final key =
        ValueKey<String>(DateTime.now().millisecondsSinceEpoch.toString());
    logger.i('ai-generation:creating staging directory [$durationTracker]');
    final stagingDirectory = await StorageUtils().getStagingDirectory(key);
    final outPath = '${stagingDirectory.path}/${path.basename(input.path)}.mp4';

    logger.i('ai-generation:output path: $outPath [$durationTracker]');

    final command = '-i ${input.path} -vf scale=854:480 -c:v libx264 '
        '-preset ultrafast -crf 28 -c:a aac -b:a 96k $outPath';

    logger.i('ai-generation:executing FFmpeg command [$durationTracker]');
    logger.i('ai-generation:FFmpeg command: $command [$durationTracker]');

    await FFmpegKit.execute(command);

    logger.i('ai-generation:FFmpeg execution completed [$durationTracker]');

    final compressedFile = FileSystemInjector.get().file(outPath);
    logger.i(
        'ai-generation:video compression finished: ${path.basename(compressedFile.path)} [$durationTracker]');

    return compressedFile;
  }
}
