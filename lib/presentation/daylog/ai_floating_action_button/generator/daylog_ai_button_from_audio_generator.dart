import 'dart:async';

import 'package:bitacora/analytics/analytics_events.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/presentation/daylog/ai_floating_action_button/generator/daylog_ai_button_generator.dart';
import 'package:bitacora/presentation/daylog/audio/simple_audio_recorder_controller.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:bitacora/util/speech_to_text/speech_to_text.dart';
import 'package:firebase_vertexai/firebase_vertexai.dart';
import 'package:flutter/cupertino.dart';
import 'package:injectzone/injectzone.dart';
import 'package:mime/mime.dart';

class DaylogAiButtonFromAudioGenerator extends DaylogAiButtonSourceGenerator {
  final ValueNotifier<SimpleAudioRecorderController?>
      audioRecorderControllerNotifier;

  DaylogAiButtonFromAudioGenerator(
    super.contextSnapshot,
    this.audioRecorderControllerNotifier,
  );

  @override
  DaylogAiButtonSource get source => DaylogAiButtonSource.audio;

  @override
  AnalyticsEvent get event => AnalyticsEvent.aiAudioEntrySelected;

  @override
  Future<Entry> runInternal() async {
    logAnalytics();

    logger.i('ai-generation:from audio process started');
    durationTracker.markStart();

    logger.i('ai-generation:initializing speech-to-text [$durationTracker]');
    final stt = Injectzone().inject(() => SpeechToTextController());

    logger.i(
        'ai-generation:starting speech-to-text listening [$durationTracker]');
    await stt.listen();

    logger.i('ai-generation:starting audio recording [$durationTracker]');
    final file = await audioRecorderControllerNotifier.value!.startRecording();

    logger.i('ai-generation:stopping speech-to-text [$durationTracker]');
    await stt.stop();
    audioRecorderControllerNotifier.value = null;

    logger.i('ai-generation:audio recording completed [$durationTracker]');

    final transcription = stt.recognizedWords;
    logger.i(
        'ai-generation:transcription result: "$transcription" [$durationTracker]');

    final Part? part;
    if (transcription.isNotEmpty) {
      logger.i(
          'ai-generation:using transcription as text input [$durationTracker]');
      part = TextPart(transcription);
    } else {
      logger.i(
          'ai-generation:transcription empty, uploading audio file [$durationTracker]');
      final fileUri = (await uploadTempFiles([file])).first;
      final mimeType = lookupMimeType(file.path)!;
      logger.i(
          'ai-generation:audio file uploaded, mime type: $mimeType [$durationTracker]');
      part = FileData(mimeType, fileUri);
    }

    logger.i(
        'ai-generation:starting parallel operations (AI generation + location) [$durationTracker]');
    final (generatedEntry, location) =
        await (generate([part]), getEntryLocation()).wait;
    logger.i(
        'ai-generation:AI generation and location retrieval completed [$durationTracker]');

    if (transcription.isEmpty) {
      unawaited(deleteTempFiles([file]));
    }

    logger.i(
        'ai-generation:processing audio file to attachment [$durationTracker]');
    final attachment = await processFileToAttachment(file);
    logger
        .i('ai-generation:attachment processing completed [$durationTracker]');

    logger.i('ai-generation:creating final entry object [$durationTracker]');
    final finalEntry = generatedEntry.copyWith(
      attachments: [],
      tags: [],
      location: location,
      source: generatedEntry.source!.copyWith(attachments: [attachment]),
    );

    logger.i(
        'ai-generation:from audio process completed successfully [$durationTracker]');

    return finalEntry;
  }
}
