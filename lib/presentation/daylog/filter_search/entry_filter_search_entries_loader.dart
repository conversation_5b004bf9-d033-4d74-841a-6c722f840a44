import 'dart:async';

import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry/filter/entry_filter.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/presentation/daylog/filter_search/entry_filter_search_all_repository_query.dart';
import 'package:bitacora/util/infinity_loader.dart';
import 'package:bitacora/util/limit_offset_cursor.dart';
import 'package:bitacora/util/list_entry/list_entry.dart';
import 'package:bitacora/util/throttler.dart';

class EntryFilterSearchEntriesLoader {
  final Repository db;
  final String pattern;
  final User user;
  final Organization org;
  final EntryFilter filter;

  final _mutationThrottler = ThrottlerInjector().get();

  InfinityLoader<ListEntry>? _infinityLoader;
  StreamSubscription? _mutationStreamSubscription;

  EntryFilterSearchEntriesLoader({
    required this.db,
    required this.pattern,
    required this.user,
    required this.org,
    required this.filter,
  });

  void loadMoreThan(int n) {
    _infinityLoader ??= InfinityLoader<ListEntry>((limit, offset) async {
      final rawList = await _loadPage(LimitOffsetCursor(limit, offset));
      return rawList.map((e) => ListEntry(e, null)).toList(growable: false);
    });

    _mutationStreamSubscription ??= db.entry.getMutations().listen((event) {
      _mutationThrottler.add(() {
        _infinityLoader!.reloadToCurrentPage();
      });
    });
    _infinityLoader!.loadMoreThan(n);
  }

  Stream<List<ListEntry>>? get entries => _infinityLoader?.items;

  StreamState get streamState => _infinityLoader == null
      ? StreamState.staging
      : _infinityLoader!.streamState;

  void dispose() {
    _disposeInfinityLoader();
  }

  Future<List<Entry>> _loadPage(LimitOffsetCursor cursor) async {
    final queryContext = db.context(
      cursor: cursor,
      queryScope: db.queryScope(
        userId: user.id,
        orgId: org.id,
      ),
    );

    return db.query(
      EntryFilterSearchAllRepositoryQuery(
        pattern: pattern,
        filter: filter,
      ),
      context: queryContext,
    );
  }

  void _disposeInfinityLoader() {
    _infinityLoader?.dispose();
    _infinityLoader = null;
    _mutationStreamSubscription?.cancel();
    _mutationStreamSubscription = null;
  }
}
